t.WebRTC-naplókSzöveges WebRTC-naplók ($1)WebRTC-eseménynaplók ($1)WebRTC napló rögzítésének ideje: $1Helyi fájl:Ninc<PERSON> helyi naplófájl.Feltöltve ekkor: $1A feltöltés sikertelen ($1).Jelentésazonosító: $1Programhiba bejegyzéseFüggőben lévő naplófeltöltés.A napló feltöltése folyamatban van.A napló nincs feltöltve.Napló helyi azonosítója: $1.Nincsenek mostanában rögzített szöveges WebRTC-naplói.Nincsenek mostanában rögzített WebRTC-eseménynaplói.Kattintson jobb gombbal a(z) $1 futtatásáhozA(z) $1 beépülő modult letiltotta a vállalati házirendA(z) $1 futtatásához az Ön engedélyére van szükségA(z) $1 le van tiltva.A(z) $1 érték már nem támogatottHelyreállítja az oldalakat?használati statisztikákA böngészési adatok automatikusan törlésre kerültekA folytatáshoz szabadítson fel tárhelyetLehetséges, hogy néhány funkció nem működik a(z) $1 helyen, amíg nem törli a más webhelyek által az Ön eszközén tárolt adatokat.Webhelyek kiválasztása a törléshezNem támogatott parancssori jelzőt használ: $1. Ennek a stabilitás és a biztonság látja kárát.Nem támogatott funkciókapcsolót használ: $1. Ennek a stabilitás és a biztonság látja kárát.Nem támogatott környezeti változót használ: $1. Ennek a stabilitás és a biztonság látja kárát.Nem biztonságos tartalom letiltvaAz oldal nem hiteles forrásokból próbál szkripteket betölteni.Nem biztonságos szkriptek betöltéseAdja hozzá ezt a webhelyet a polchoz, hogy bármikor használhassaA rendszerrőlRendszerdiagnosztikai adatokRészletek: $1Összes részletes nézete…Összes listanézete…Részletes nézet…Listanézet…Nem sikerült a fájl szintaktikai elemzése: $1Régi böngészők támogatásaMegnyitás másik böngészőben mostMegnyitás most a(z) $1 böngészőbenNem sikerült megnyitni a másik böngészőtA(z) $1 megnyitása nem sikerültMegnyitás másik böngészőben $1 másodperc múlvaMegnyitás a(z) $2 böngészőben $1 másodperc múlva…A(z) $1 $1-címet nem sikerült másik böngészőben megnyitni. Kérjük, forduljon a rendszergazdához.Nem sikerült a(z) $1 webhely megnyitása a(z) $2 böngészőben. Forduljon a rendszergazdához.A böngészőátirányítások esetén csak a http-, https- és fájlprotokollok tám